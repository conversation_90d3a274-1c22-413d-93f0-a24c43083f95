<template>
  <div class="flight-container">
    <div class="map-wrapper">
      <Amap ref="amap" :coordinates="coordinates" :zoom="13" />

      <!-- 鼠标经纬度显示 -->
      <div class="mouse-coordinates" v-if="showMouseCoordinates && mouseCoordinates.lng && mouseCoordinates.lat">
        <div class="coordinates-content">
          <div class="coordinate-line">
            <span class="coordinate-system">WGS84：</span>
            <span class="coordinate-value">{{ wgs84Coordinates.lng }},{{ wgs84Coordinates.lat }}</span>
          </div>
          <div class="coordinate-line">
            <span class="coordinate-system">GCJ02：</span>
            <span class="coordinate-value">{{ gcj02Coordinates.lng }},{{ gcj02Coordinates.lat }}</span>
          </div>
        </div>
      </div>

      <!-- 坐标显示控制按钮 -->
      <div class="coordinate-control-btn" v-if="!showMouseCoordinates">
        <el-tooltip content="显示鼠标坐标" placement="top">
          <el-button type="primary" size="mini" @click="showMouseCoordinates = true" class="show-coordinates-btn">
            <i class="el-icon-location"></i>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 地图工具栏 -->
      <div class="map-toolbar">
        <el-tooltip content="测距" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggleMeasureDistance"
            :class="{ 'active-tool': currentMapTool === 'distance' }"
            class="map-tool-btn">
            <svg-icon icon-class="map_cj" />
          </el-button>
        </el-tooltip>
        <el-tooltip content="测面积" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggleMeasureArea"
            :class="{ 'active-tool': currentMapTool === 'area' }"
            class="map-tool-btn">
            <svg-icon icon-class="map_cm" />
          </el-button>
        </el-tooltip>
        <el-tooltip content="清除测量" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="clearMeasurements"
            class="map-tool-btn map-tool-btn-clear">
            <i class="el-icon-delete"></i>
          </el-button>
        </el-tooltip>
        <el-tooltip content="路况信息" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggleTrafficLayer"
            :class="{ 'active-tool': showTrafficLayer }"
            class="map-tool-btn">
            <svg-icon icon-class="map_lk" />
          </el-button>
        </el-tooltip>
        <el-tooltip content="卫星图" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggleSatelliteLayer"
            :class="{ 'active-tool': showSatelliteLayer }"
            class="map-tool-btn">
            <svg-icon icon-class="map_wxt" />
          </el-button>
        </el-tooltip>
      </div>

      <div class="video-containers-wrapper">
        <div v-for="(video, index) in videos" :key="index" class="video-container-wrapper">
          <div class="video-container">
            <!-- 状态图标和控制按钮 -->
            <div class="video-top-controls">
              <!-- 根据domain判断显示不同的状态图标 -->


              <!--########## 无人机状态图标########## (domain=0) -->
              <div class="drone-status-icons"
                v-if="getDeviceDomain(video.deviceSn) === 0 && getDroneOsd(video.deviceSn)">
                <!-- 电池状态图标 -->
                <el-tooltip :content="getDroneBatteryTooltip(video.deviceSn)" placement="top"
                  v-if="getDroneOsd(video.deviceSn).battery">
                  <!-- 双电池垂直布局 -->
                  <div v-if="hasDualBatteries(getDroneOsd(video.deviceSn).battery)"
                    class="status-icon battery-icon dual-battery"
                    :class="getDroneBatteryClass(getDroneOsd(video.deviceSn).battery)">
                    <svg-icon :icon-class="getDroneBatteryIcon(getDroneOsd(video.deviceSn).battery.capacity_percent)"
                      class="battery-svg-icon" />
                    <div class="dual-battery-text">
                      <div class="battery-percent">{{ getDroneOsd(video.deviceSn).battery.batteries[0].capacity_percent
                        }}%</div>
                      <div class="battery-percent">{{ getDroneOsd(video.deviceSn).battery.batteries[1].capacity_percent
                        }}%</div>
                    </div>
                  </div>
                  <!-- 单电池水平布局 -->
                  <div v-else class="status-icon battery-icon"
                    :class="getDroneBatteryClass(getDroneOsd(video.deviceSn).battery)">
                    <svg-icon :icon-class="getDroneBatteryIcon(getDroneOsd(video.deviceSn).battery.capacity_percent)"
                      class="battery-svg-icon" />
                    <span class="battery-percent">{{ getDroneOsd(video.deviceSn).battery.capacity_percent }}%</span>
                  </div>
                </el-tooltip>

                <!-- GPS/RTK状态图标 -->
                <el-tooltip :content="getDroneGpsTooltip(video.deviceSn)" placement="top"
                  v-if="getDroneOsd(video.deviceSn).position_state">
                  <div class="status-icon gps-icon">
                    <i class="el-icon-location"></i>
                    <div class="gps-info-container">
                      <div class="gps-line">GPS:{{ getDroneOsd(video.deviceSn).position_state.gps_number || 0 }}</div>
                      <div class="rtk-line">RTK:{{ getDroneOsd(video.deviceSn).position_state.rtk_number || 0 }}</div>
                    </div>
                  </div>
                </el-tooltip>
              </div>



              <!-- ##########机场状态图标########## (domain=3) -->
              <div class="drone-status-icons"
                v-else-if="getDockDomain(video.deviceSn) === 3 && getDockOsdData(video.deviceSn)">
                <!-- 电池状态图标 -->
                <el-tooltip :content="getBatteryTooltip(video.deviceSn)" placement="top"
                  v-if="getDockOsdData(video.deviceSn).drone_charge_state">
                  <div class="status-icon battery-icon"
                    :class="getBatteryClass(osdDock[video.deviceSn].drone_charge_state)">
                    <svg-icon :icon-class="getBatteryIcon(osdDock[video.deviceSn].drone_charge_state)"
                      class="battery-svg-icon" />
                    <span class="battery-percent">{{ getDockOsdData(video.deviceSn).drone_charge_state.capacity_percent
                      }}%</span>
                  </div>
                </el-tooltip>

                <!-- GPS/RTK状态图标 -->
                <el-tooltip :content="getGpsTooltip(getDockOsdData(video.deviceSn))" placement="top"
                  v-if="getDockOsdData(video.deviceSn).position_state">
                  <div class="status-icon gps-icon">
                    <i class="el-icon-location"></i>
                    <div class="gps-info-container">
                      <div class="gps-line">GPS:{{ getDockOsdData(video.deviceSn).position_state.gps_number || 0 }}
                      </div>
                      <div class="rtk-line">RTK:{{ getDockOsdData(video.deviceSn).position_state.rtk_number || 0 }}
                      </div>
                    </div>
                  </div>
                </el-tooltip>
                <!-- 网络状态图标 -->
                <el-tooltip :content="getNetworkTooltip(getDockOsdData(video.deviceSn))" placement="top">
                  <div class="status-icon network-icon">
                    <div class="network-icon-container">
                      <svg-icon :icon-class="getNetworkIcon(getDockOsdData(video.deviceSn).network_state)"
                        class="network-svg-icon" />
                      <span class="network-type-label">
                        {{ getlinkWorkmodeTypeText(getAirportInfo(video.deviceSn).wireless_link.link_workmode) }}
                      </span>
                    </div>
                  </div>
                </el-tooltip>
              </div>



              <!--显示无人机的mode_code状态-->
              <div class="control-buttons">
                <!-- 只显示无人机 -->
                <div class="mode-code-display" v-if="getDeviceDomain(video.deviceSn) === 0">
                  <el-tooltip :content="getModeCodeTooltip(video.deviceSn)" placement="top">
                    <div class="mode-code-badge">
                      <span class="mode-code-value">{{ getDroneModeCodeDisplayText(video.deviceSn) }}</span>
                    </div>
                  </el-tooltip>
                </div>

                <!-- 调试按钮 - 只在机场视频框显示，无人机视频框隐藏 -->
                <el-button v-if="getDeviceDomain(video.deviceSn) !== 0" type="primary" icon="el-icon-setting"
                  @click="toggleDebugPanel(index)" class="debug-btn" title="调试" size="mini">
                  调试
                </el-button>
              </div>
            </div>

            <TCPlayerVideo :src="video.src" :device-name="video.deviceName" :width="400" :height="250" :autoplay="false"
              :muted="true" :license-url="tcPlayerLicenseUrl" @close="closeVideo(index)" />

            <!-- 根据domain显示不同的信息面板 -->
            <!-- 无人机信息展示区域 (domain=0) -->
            <div class="airport-info-panel" v-if="getDeviceDomain(video.deviceSn) === 0 && getDroneOsd(video.deviceSn)">
              <div class="info-grid">
                <!-- 风向 -->
                <el-tooltip v-if="getDroneOsd(video.deviceSn).wind_direction !== undefined"
                  :content="`当前风向: ${getWindDirectionText(getDroneOsd(video.deviceSn).wind_direction)}`"
                  placement="top">
                  <div class="info-item">
                    <div class="info-icon wind-direction-icon">
                      🧭
                    </div>
                    <div class="info-value">
                      {{ getWindDirectionText(getDroneOsd(video.deviceSn).wind_direction) }}
                    </div>
                  </div>
                </el-tooltip>

                <!-- 风速 -->
                <el-tooltip v-if="getDroneOsd(video.deviceSn).wind_speed !== undefined"
                  :content="`当前风速${formatDroneWindSpeed(getDroneOsd(video.deviceSn).wind_speed)}`" placement="top">
                  <div class="info-item">
                    <div class="info-icon wind-speed-icon">
                      💨
                    </div>
                    <div class="info-value">
                      {{ formatDroneWindSpeed(getDroneOsd(video.deviceSn).wind_speed) }}
                    </div>
                  </div>
                </el-tooltip>

                <!-- 距离Home点的距离 -->
                <el-tooltip v-if="getDroneOsd(video.deviceSn).home_distance !== undefined"
                  :content="`距离Home点${formatHomeDistance(getDroneOsd(video.deviceSn).home_distance)}`" placement="top">
                  <div class="info-item">
                    <div class="info-icon home-distance-icon">
                      🏠
                    </div>
                    <div class="info-value">
                      {{ formatHomeDistance(getDroneOsd(video.deviceSn).home_distance) }}
                    </div>
                  </div>
                </el-tooltip>
              </div>
            </div>

            <!-- 机场环境信息展示区域 (domain=3) -->
            <div class="airport-info-panel"
              v-else-if="getDockDomain(video.deviceSn) === 3 && getAirportInfo(video.deviceSn)">
              <div class="info-grid">
                <!-- 温度 -->
                <el-tooltip v-if="getAirportInfo(video.deviceSn).temperature !== undefined"
                  :content="`舱内温度${getAirportInfo(video.deviceSn).temperature.toFixed(1)}摄氏度`" placement="top">
                  <div class="info-item">
                    <div class="info-icon temperature-icon">
                      🌡️
                    </div>
                    <div class="info-value">
                      {{ formatTemperature(getAirportInfo(video.deviceSn).temperature) }}
                    </div>
                  </div>
                </el-tooltip>

                <!-- 湿度 -->
                <el-tooltip v-if="getAirportInfo(video.deviceSn).humidity !== undefined"
                  :content="`舱内相对湿度${getAirportInfo(video.deviceSn).humidity.toFixed(1)}%`" placement="top">
                  <div class="info-item">
                    <div class="info-icon humidity-icon">
                      💧
                    </div>
                    <div class="info-value">
                      {{ formatHumidity(getAirportInfo(video.deviceSn).humidity) }}
                    </div>
                  </div>
                </el-tooltip>

                <!-- 风速 -->
                <el-tooltip v-if="getAirportInfo(video.deviceSn).wind_speed !== undefined"
                  :content="`地面风速${getAirportInfo(video.deviceSn).wind_speed.toFixed(1)}米每秒`" placement="top">
                  <div class="info-item">
                    <div class="info-icon wind-icon">
                      💨
                    </div>
                    <div class="info-value">
                      {{ formatWindSpeed(getAirportInfo(video.deviceSn).wind_speed) }}
                    </div>
                  </div>
                </el-tooltip>

                <!-- 降雨量 -->
                <div class="info-item" v-if="getAirportInfo(video.deviceSn).rainfall !== undefined">
                  <div class="info-icon rainfall-icon">
                    🌧️
                  </div>
                  <div class="info-value">
                    {{ formatRainfall(getAirportInfo(video.deviceSn).rainfall) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 调试面板 -->
          <div v-if="video.showDebugPanel" class="debug-panel">
            <div class="debug-panel-header">
              <span class="debug-title">调试信息</span>
              <el-button type="text" icon="el-icon-close" @click="closeDebugPanel(index)" class="debug-close-btn"
                size="mini" />
            </div>

            <div class="debug-panel-content">
              <div v-if="video.debugLoading" class="debug-loading">
                <i class="el-icon-loading"></i>
                <span>加载调试信息中...</span>
              </div>

              <div v-else-if="video.debugError" class="debug-error">
                <i class="el-icon-warning"></i>
                <span>{{ video.debugError }}</span>
                <el-button type="primary" size="mini" @click="refreshDebugInfo(index)" style="margin-top: 10px;">
                  重试
                </el-button>
              </div>

              <div v-else-if="video.debugInfo" class="debug-info">
                <div class="debug-sections-horizontal">
                  <!-- Dock 调试区域 -->
                  <div class="debug-section-left">

                    <div class="debug-section-title">
                      <el-button type="primary" size="mini" @click="handleDeviceAction(video.deviceSn, 'device_reboot')"
                        class="debug-action-btn">重启</el-button>
                      <span class="debug-section-text">机场</span>
                      <el-button type="warning" size="mini" @click="handleDeviceAction(video.deviceSn, 'device_format')"
                        class="debug-action-btn">格式化</el-button>
                    </div>
                    <div v-for="item in video.debugInfo.dock" :key="item.title" class="debug-control-item">
                      <div class="debug-control-row">
                        <div class="debug-control-label">{{ item.title }}</div>

                        <!-- 滑动开关 (list长度为2) -->
                        <div v-if="item.list && item.list.length === 2" class="debug-control-widget">
                          <DebugSwitch :value="item.val === '1'" :active-text="getSwitchLabel(item.list, '1') || '开启'"
                            :inactive-text="getSwitchLabel(item.list, '0') || '关闭'"
                            @change="handleSwitchChange(video.deviceSn, getSwitchTargetUrl(item.list, $event), getSwitchTargetValue(item.list, $event))" />
                        </div>

                        <!-- 下拉选择框 (list长度大于2) -->
                        <div v-else-if="item.list && item.list.length > 2" class="debug-control-widget">
                          <el-select :value="item.val"
                            @change="handleSelectChange(video.deviceSn, getSelectTargetUrl(item.list, $event), $event)"
                            placeholder="请选择" size="mini">
                            <el-option v-for="option in item.list" :key="option.value" :label="option.label"
                              :value="option.value" />
                          </el-select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Drone 调试区域 -->
                  <div class="debug-section-right">
                    <div class="debug-section-title">

                      <span class="debug-section-text">飞行器</span>
                      <el-button type="warning" size="mini" @click="handleDeviceAction(video.deviceSn, 'drone_format')"
                        class="debug-action-btn">格式化</el-button>
                    </div>
                    <div v-for="item in video.debugInfo.drone" :key="item.title" class="debug-control-item">
                      <div class="debug-control-row">
                        <div class="debug-control-label">{{ item.title }}</div>

                        <!-- 滑动开关 (list长度为2) -->
                        <div v-if="item.list && item.list.length === 2" class="debug-control-widget">
                          <DebugSwitch :value="item.val === '1'" :active-text="getSwitchLabel(item.list, '1') || '开启'"
                            :inactive-text="getSwitchLabel(item.list, '0') || '关闭'"
                            @change="handleSwitchChange(video.deviceSn, getSwitchTargetUrl(item.list, $event), getSwitchTargetValue(item.list, $event))" />
                        </div>

                        <!-- 下拉选择框 (list长度大于2) -->
                        <div v-else-if="item.list && item.list.length > 2" class="debug-control-widget">
                          <el-select :value="item.val"
                            @change="handleSelectChange(video.deviceSn, getSelectTargetUrl(item.list, $event), $event)"
                            placeholder="请选择" size="mini">
                            <el-option v-for="option in item.list" :key="option.value" :label="option.label"
                              :value="option.value" />
                          </el-select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="debug-empty">
                <span>暂无调试信息</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <div class="flight-sidebar">
      <div class="flight-sidebar-header">
        <el-row type="flex" align="middle">
          <el-col :span="24">
            <svg-icon icon-class="jichang_list" class="sidebar-icon" />
            设备列表
          </el-col>
        </el-row>
      </div>
      <div class="flight-airport-list">

        <!--#################机场面板############### -->
        <div v-for="(coord, index) in deviceList" :key="index">
          <!-- 机场面板 - 只有当机场在线时才显示 (mode_code != -1) -->
          <el-card v-if="isDockOnline(coord)" class="flight-airport-item" shadow="hover"
            @click.native="handleDeviceClick(coord)">
            <!-- 头部信息 -->
            <el-row type="flex" align="middle">
              <el-col :span="24">
                <div class="airport-title">
                  <div class="airport-image" style="width: 2rem; height: 2rem;">
                    <img src="@/assets/images/wrj_jc.png" alt="机场图片" class="airport-img" />
                  </div>
                  <div style="padding-left: 1rem;font-size: 1rem;">{{ coord.nickname }}</div>

                  <el-tooltip class="item" effect="dark" content="定位" placement="right">
                    <el-button type="text" icon="el-icon-map-location" @click.stop="locateAirport(coord)"
                      class="locate-button" />
                  </el-tooltip>
                </div>
              </el-col>
            </el-row>
            <!-- 机场信息 -->
            <div class="airport-divider"></div>
            <el-row style="margin: 0.5rem 0px;">
              <el-col :span="7" class="dockstate">
                <el-tag size="medium" :type="getStateType(coord.mode_code, 3)"
                  style="width: 100%; display: flex; justify-content: center;">
                  {{ getStateLabel(coord.mode_code, 3) }}
                </el-tag>
              </el-col>
              <el-col :span="17">
                <el-tooltip class="item" effect="dark" :content="coord.task_name" placement="top"
                  :disabled="!isTextOverflow">
                  <div class="task-name" ref="taskName" @mouseenter="checkTextOverflow">
                    {{ coord.task_name }}
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
            <!-- 机场的无人机信息 -->
            <el-row>
              <el-col :span="7" class="dockstate">
                <el-tag size="medium" :type="getStateType(coord.children.mode_code, 0)"
                  style="width: 100%; display: flex; justify-content: center;">
                  {{ getStateLabel(coord.children.mode_code, 0) }}
                </el-tag>
              </el-col>
              <el-col :span="17">
                <el-tooltip class="item" effect="dark" :content="coord.children ? coord.children.nickname : '无设备'"
                  placement="top" :disabled="!isTextOverflow">
                  <div class="task-name" ref="taskName" @mouseenter="checkTextOverflow">
                    {{ coord.children ? coord.children.nickname : '无设备' }}
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
          </el-card>

          <!-- 无人机面板 - 只有当机场在线且无人机在线时才显示 -->
          <el-card v-if="isDockOnline(coord) && isDroneOnline(coord)" class="flight-airport-item" shadow="hover"
            @click.native="handleDeviceClick(coord.children)">
            <!-- 头部信息 -->
            <el-row type="flex" align="middle">
              <el-col :span="24">
                <div class="airport-title">
                  <div class="airport-image" style="width: 2rem; height: 2rem;">
                    <img src="@/assets/images/wrj.png" alt="无人机图片" class="airport-img" />
                  </div>
                  <div style="padding-left: 1rem;font-size: 1rem;">{{ coord.children.nickname }}</div>

                  <el-tooltip class="item" effect="dark" content="定位" placement="right">
                    <el-button type="text" icon="el-icon-map-location" @click.stop="locateAirport(coord)"
                      class="locate-button" />
                  </el-tooltip>
                </div>
              </el-col>
            </el-row>
            <!--  -->
            <div class="airport-divider"></div>
            <el-row style="margin: 0.5rem 0px;">

            </el-row>
            <!-- 无人机信息 -->
            <el-row>
            </el-row>
          </el-card>
        </div>
      </div>
    </div>

    <!-- WebSocket状态显示 -->
    <div class="websocket-status">
      <div class="status-indicator" :class="websocketStatus">
        <span class="status-dot"></span>
        <span class="status-text">
          WebSocket:
          <span v-if="websocketStatus === 'connected'">已连接</span>
          <span v-else-if="websocketStatus === 'connecting'">连接中...</span>
          <span v-else-if="websocketStatus === 'error'">连接失败</span>
          <span v-else>未连接</span>
        </span>
      </div>
      <div v-if="websocketError" class="error-message">{{ websocketError }}</div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import TCPlayerVideo from '@/components/TCPlayerVideo.vue'
import DebugSwitch from '@/components/DebugSwitch.vue'

// 导入mixins
import websocketMixin from './mixins/websocketMixin.js'
import debugPanelMixin from './mixins/debugPanelMixin.js'
import deviceManagementMixin from './mixins/deviceManagementMixin.js'
import mapMixin from './mixins/mapMixin.js'

export default {
  mixins: [websocketMixin, debugPanelMixin, deviceManagementMixin, mapMixin],
  components: {
    TCPlayerVideo,
    DebugSwitch
  },
  data() {
    return {
      droneModeCode: 0,
      dockModeCode: 1,
      // TCPlayer License URL (需要申请腾讯云License)
      // 临时注释掉License URL，用于开发测试
      tcPlayerLicenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1303164718_1/v_cube.license',
      // 鼠标经纬度坐标
      mouseCoordinates: {
        lng: null,
        lat: null
      },
      // 是否显示鼠标坐标
      showMouseCoordinates: true,
      // 当前地图工具
      currentMapTool: null,
      // 测距工具实例
      distanceTool: null,
      // 测面积工具实例
      areaTool: null,
      // 测量结果列表
      measurementResults: [],
      // 路况图层显示状态
      showTrafficLayer: false,
      // 卫星图层显示状态
      showSatelliteLayer: false,
      // 路况图层实例
      trafficLayer: null,
      // 卫星图层实例
      satelliteLayer: null
    };
  },

  computed: {
    // 地图坐标数据 - 从设备列表中提取
    coordinates() {
      if (!this.deviceList || !Array.isArray(this.deviceList)) {
        return [];
      }

      return this.deviceList
        .filter(device => device.longitude && device.latitude)
        .map(device => [device.longitude, device.latitude]);
    },

    // WGS84 坐标
    wgs84Coordinates() {
      if (!this.mouseCoordinates.lng || !this.mouseCoordinates.lat) {
        return { lng: '--', lat: '--' };
      }

      const lng = parseFloat(this.mouseCoordinates.lng);
      const lat = parseFloat(this.mouseCoordinates.lat);

      // 将 GCJ02 转换为 WGS84
      const wgs84Coord = this.gcj02ToWgs84(lng, lat);
      return {
        lng: wgs84Coord.lng.toFixed(6),
        lat: wgs84Coord.lat.toFixed(6)
      };
    },

    // GCJ02 坐标（高德地图原始坐标）
    gcj02Coordinates() {
      if (!this.mouseCoordinates.lng || !this.mouseCoordinates.lat) {
        return { lng: '--', lat: '--' };
      }

      const lng = parseFloat(this.mouseCoordinates.lng);
      const lat = parseFloat(this.mouseCoordinates.lat);

      return {
        lng: lng.toFixed(6),
        lat: lat.toFixed(6)
      };
    }
  },

  created() {
    // 进入页面时隐藏导航栏
    this.toggleNavbar(false)
    this.getDockStateDict()

    //获取设备列表
    this.getDeviceInfo();

    // 添加 WebSocket 连接
    this.connectWebSocket();
    // 启动设备超时检测
    this.startTimeoutCheck();
  },

  //* 关闭页面调用的全局方法*/
  beforeDestroy() {
    // 移除页面刷新监听器
    window.removeEventListener('beforeunload', this.handlePageUnload);

    // 使用 nextTick 确保状态更新后再离开页面
    this.$nextTick(async () => {
      this.toggleNavbar(true);

      // 关闭所有打开的调试模式
      await this.closeAllDebugModes();

      // 清理WebSocket资源
      this.cleanupWebSocket();

      // 清理地图图层
      this.cleanupMapLayers();
    });
  },

  async beforeRouteLeave(to, from, next) {
    this.toggleNavbar(true);

    // 关闭所有打开的调试模式
    await this.closeAllDebugModes();

    next();
  },

  mounted() {
    // 在地图加载完成后初始化信息窗体
    this.$nextTick(() => {
      if (this.$refs.amap && this.$refs.amap.map) {
        // 创建信息窗体
        this.infoWindow = new AMap.InfoWindow({
          isCustom: true,  // 使用自定义窗体
          content: this.createInfoWindowContent('', 0), // 初始为空内容
          offset: new AMap.Pixel(0, -10), // 设置偏移量
          closeWhenClickMap: true // 点击地图关闭信息窗体
        });

        // 初始化设备标记
        setTimeout(() => {
          this.updateAllDeviceMarkers();
        }, 500);

        // 添加鼠标移动事件监听
        this.initMouseCoordinatesListener();
      }
    });

    // 监听页面刷新/关闭事件，自动关闭所有调试模式
    window.addEventListener('beforeunload', this.handlePageUnload);

    // 添加测试OSD数据（用于测试状态图标）
    setTimeout(() => {
      this.addTestOsdData();
    }, 1000);
  },

  watch: {
    // 监听调试面板显示状态变化
    videos: {
      handler(newVideos, oldVideos) {
        if (!oldVideos) return;
      },
      deep: true
    },

    // 监听设备列表变化，更新地图标记
    deviceList: {
      handler(newDeviceList, oldDeviceList) {
        if (!newDeviceList || !Array.isArray(newDeviceList)) return;

        // 延迟更新，确保地图已初始化
        this.$nextTick(() => {
          setTimeout(() => {
            this.updateAllDeviceMarkers();
          }, 100);
        });
      },
      deep: true
    }
  },

  methods: {
    ...mapActions('app', ['toggleNavbar']),

    // 初始化鼠标坐标监听器
    initMouseCoordinatesListener() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        console.warn('地图组件未初始化，无法添加鼠标坐标监听');
        return;
      }

      const map = this.$refs.amap.map;

      // 节流函数，避免频繁更新
      let throttleTimer = null;
      const throttleDelay = 100; // 100ms 节流延迟

      // 添加鼠标移动事件监听
      map.on('mousemove', (e) => {
        if (e.lnglat) {
          // 清除之前的定时器
          if (throttleTimer) {
            clearTimeout(throttleTimer);
          }

          // 设置新的定时器
          throttleTimer = setTimeout(() => {
            // 获取鼠标位置的经纬度
            const lng = e.lnglat.getLng();
            const lat = e.lnglat.getLat();

            // 格式化经纬度显示（保留6位小数）
            this.mouseCoordinates.lng = lng.toFixed(6);
            this.mouseCoordinates.lat = lat.toFixed(6);
          }, throttleDelay);
        }
      });

      // 添加鼠标离开地图事件监听
      map.on('mouseleave', () => {
        // 清除定时器
        if (throttleTimer) {
          clearTimeout(throttleTimer);
          throttleTimer = null;
        }
        // 鼠标离开地图时清空坐标显示
        this.mouseCoordinates.lng = null;
        this.mouseCoordinates.lat = null;
      });

      // 添加鼠标进入地图事件监听
      map.on('mouseenter', () => {
        // 鼠标进入地图时显示坐标（如果有的话）
        //console.log('鼠标进入地图区域');
      });

      //console.log('鼠标坐标监听器初始化完成');
    },





    // 获取机场环境信息
    getAirportInfo(deviceSn) {
      return this.osdDock[deviceSn];
    },

    // 格式化温度显示
    formatTemperature(temperature) {
      if (temperature === undefined || temperature === null) return '--';
      return `${temperature.toFixed(1)}°C`;
    },

    // 格式化湿度显示
    formatHumidity(humidity) {
      if (humidity === undefined || humidity === null) return '--';
      return `${humidity.toFixed(1)}%`;
    },

    // 格式化风速显示
    formatWindSpeed(windSpeed) {
      if (windSpeed === undefined || windSpeed === null) return '--';
      return `${windSpeed.toFixed(1)}m/s`;
    },

    // 格式化降雨量显示
    formatRainfall(rainfall) {
      if (rainfall === undefined || rainfall === null) return '--';
      return `${rainfall.toFixed(1)}mm`;
    },

    // 获取无人机的domain
    getDeviceDomain(deviceSn) {
      const device = this.deviceList.find(d => d.children.device_sn === deviceSn);
      if (!device) return null;
      return device.children.domain;
    },
    //获取机场
    getDockDomain(deviceSn) {
      const device = this.deviceList.find(d => d.device_sn === deviceSn);
      if (!device) return null;
      return device.domain;
    },

    // 获取无人机OSD数据（恢复原来的逻辑）
    getDockOsdData(deviceSn) {
      const dockOsd = this.osdDock[deviceSn];
      if (!dockOsd) return null;

      // 返回包含无人机相关状态的数据
      return {
        drone_charge_state: dockOsd.drone_charge_state,
        position_state: dockOsd.position_state,
        network_state: dockOsd.network_state
      };
    },

    // 根据设备SN获取无人机OSD数据
    getDroneOsd(deviceSn) {
      return this.osdDrone[deviceSn];
    },

    // 获取无人机的mode_code
    getDroneModeCode(droneSn) {
      if (!droneSn) return null;

      // 如果OSD数据不存在，从设备列表获取
      const droneDevice = this.deviceList.find(d =>
        d.children.device_sn === droneSn
      );

      return droneDevice ? droneDevice.children.mode_code : null;
    },

    // 获取机场的mode_code
    getDockModeCode(dockSn) {
      if (!dockSn) return null;

      // 优先从实时OSD数据获取
      const dockOsd = this.osdDock[dockSn];
      if (dockOsd && dockOsd.mode_code !== undefined) {
        return dockOsd.mode_code;
      }

      // 如果OSD数据不存在，从设备列表获取
      const dockDevice = this.deviceList.find(d =>
        d.domain === 3 && d.device_sn === dockSn
      );
      return dockDevice ? dockDevice.mode_code : null;
    },

    // 根据设备SN获取对应的mode_code（兼容旧接口，但建议使用上面的明确方法）
    getModeCodeData(deviceSn) {
      const device = this.deviceList.find(d => d.device_sn === deviceSn);
      if (!device) return null;

      // 如果是无人机设备(domain=0)
      if (device.domain === 0) {
        return this.getDroneModeCode(deviceSn);
      }

      // 如果是机场设备(domain=3)，获取其子设备的mode_code
      if (device.domain === 3 && device.children) {
        return this.getDroneModeCode(device.children.device_sn);
      }

      return null;
    },

    // 获取无人机Mode Code提示文本
    getDroneModeCodeTooltip(droneSn) {
      const modeCode = this.getDroneModeCode(droneSn);
      if (modeCode === null || modeCode === undefined) return '无人机模式状态未知';

      // 无人机的domain固定为0
      const stateLabel = this.getStateLabel(modeCode, 0);
      return `无人机当前模式: ${stateLabel} (${modeCode})`;
    },

    // 获取机场Mode Code提示文本
    getDockModeCodeTooltip(dockSn) {
      const modeCode = this.getDockModeCode(dockSn);
      if (modeCode === null || modeCode === undefined) return '机场模式状态未知';

      // 机场的domain固定为3
      const stateLabel = this.getStateLabel(modeCode, 3);
      return `机场当前模式: ${stateLabel} (${modeCode})`;
    },

    // 获取无人机Mode Code显示文本
    getDroneModeCodeDisplayText(droneSn) {
      const modeCode = this.getDroneModeCode(droneSn);
      if (modeCode === null || modeCode === undefined) return '未知';

      // 无人机的domain固定为0
      const stateLabel = this.getStateLabel(modeCode, 0);
      return stateLabel || '未知';
    },

    // 获取机场Mode Code显示文本
    getDockModeCodeDisplayText(dockSn) {
      const modeCode = this.getDockModeCode(dockSn);
      if (modeCode === null || modeCode === undefined) return '未知';

      // 机场的domain固定为3
      const stateLabel = this.getStateLabel(modeCode, 3);
      return stateLabel || '未知';
    },

    // 兼容旧接口的Mode Code提示文本
    getModeCodeTooltip(deviceSn) {
      const device = this.deviceList.find(d => d.device_sn === deviceSn);
      if (!device) return '设备未找到';

      if (device.domain === 0) {
        return this.getDroneModeCodeTooltip(deviceSn);
      } else if (device.domain === 3 && device.children) {
        return this.getDroneModeCodeTooltip(device.children.device_sn);
      }

      return '模式状态未知';
    },



    // 获取风向文本
    getWindDirectionText(windDirection) {
      if (windDirection === undefined || windDirection === null) return '--';

      const directionMap = {
        1: '正北',
        2: '东北',
        3: '东',
        4: '东南',
        5: '南',
        6: '西南',
        7: '西',
        8: '西北'
      };

      return directionMap[windDirection] || '--';
    },

    // 格式化无人机风速显示 (单位：0.1 m/s)
    formatDroneWindSpeed(windSpeed) {
      if (windSpeed === undefined || windSpeed === null) return '--';
      // 风速单位是0.1 m/s，需要除以10得到实际m/s
      const actualSpeed = windSpeed / 10;
      return `${actualSpeed.toFixed(1)}m/s`;
    },

    // 格式化Home距离显示
    formatHomeDistance(homeDistance) {
      if (homeDistance === undefined || homeDistance === null) return '--';

      // 如果距离小于1000米，显示米
      if (homeDistance < 1) {
        return `${(homeDistance * 1000).toFixed(0)}m`;
      }
      // 否则显示公里
      return `${homeDistance.toFixed(2)}km`;
    },

    // 获取电池图标
    getBatteryIcon(chargeState) {
      if (!chargeState) return 'dc_1'; // 默认显示低电量图标

      const { state, capacity_percent } = chargeState;

      // 如果正在充电，显示充电图标
      if (state == true) {
        return 'dc_cd'; // 充电中
      }

      // 如果电量低于30%，显示低电量图标
      if (capacity_percent < 30) {
        return 'dc_1'; // 低电量
      }

      // 正常状态显示标准电池图标
      return 'dc_3'; // 正常电量
    },

    // 获取电池CSS类
    getBatteryClass(chargeState) {
      if (!chargeState) return '';

      const { state, capacity_percent } = chargeState;

      // 如果正在充电
      if (state === true) {
        return 'charging';
      }

      // 如果电量低于30%
      if (capacity_percent < 30) {
        return 'low';
      }

      return '';
    },

    // 获取电池提示文本
    getBatteryTooltip(sn) {
      const chargeState = this.osdDock[sn].drone_charge_state;
      if (!chargeState) return '电池状态未知';

      const { state, capacity_percent } = chargeState;
      const stateText = state === 1 ? '充电中' : '空闲';

      return `电池电量: ${capacity_percent}% (${stateText})`;
    },

    // 获取无人机电池图标
    getDroneBatteryIcon(battery) {
      if (!battery) return 'dc_1'; // 默认显示低电量图标
      const { capacity_percent } = battery;
      // 如果电量低于30%，显示低电量图标
      if (capacity_percent < 30) {
        return 'dc_1'; // 低电量
      }
      // 正常状态显示标准电池图标
      return 'dc_3'; // 正常电量
    },

    // 获取无人机电池CSS类
    getDroneBatteryClass(battery) {
      if (!battery) return '';

      const { capacity_percent, state } = battery;

      // 如果正在充电，只改变图标颜色为绿色
      if (state === true || state === 1) {
        return 'charging';
      }

      // 如果电量低于30%
      if (capacity_percent < 30) {
        return 'low';
      }

      return '';
    },

    // 检查是否有双电池
    hasDualBatteries(battery) {
      return battery && battery.batteries && Array.isArray(battery.batteries) && battery.batteries.length >= 2;
    },

    // 获取无人机电池提示文本
    getDroneBatteryTooltip(deviceSn) {
      const droneOsd = this.getDroneOsd(deviceSn);
      if (!droneOsd || !droneOsd.battery) return '电池状态未知';

      const battery = droneOsd.battery;

      // 如果有双电池，显示详细信息
      if (this.hasDualBatteries(battery)) {
        const battery1 = battery.batteries[0];
        const battery2 = battery.batteries[1];
        return `电池1电量: ${battery1.capacity_percent}%\n电池2电量: ${battery2.capacity_percent}%\n总电量: ${battery.capacity_percent}%`;
      }

      // 单电池显示
      const { capacity_percent } = battery;
      return `电池电量: ${capacity_percent}%`;
    },

    // 获取无人机GPS提示文本
    getDroneGpsTooltip(deviceSn) {
      const droneOsd = this.getDroneOsd(deviceSn);
      if (!droneOsd || !droneOsd.position_state) return 'GPS状态未知';

      const {
        gps_number,
        rtk_number,
        quality,
        is_fixed
      } = droneOsd.position_state;

      const qualityMap = {
        1: '1档', 2: '2档', 3: '3档', 4: '4档', 5: '5档', 10: 'RTK fixed'
      };

      const fixedMap = {
        0: '未开始', 1: '收敛中', 2: '收敛成功', 3: '收敛失败'
      };
      const fixedText = fixedMap[is_fixed] || '未知';

      return `GPS搜星: ${gps_number || 0}颗\nRTK搜星: ${rtk_number || 0}颗\n搜星档位: ${qualityMap[quality] || '未知'}\n收敛状态: ${fixedText}`;
    },



    // 获取GPS显示文本
    getGpsDisplayText(positionState) {
      if (!positionState) return '--';

      const { gps_number, rtk_number, quality } = positionState;

      // 显示GPS和RTK数量
      return `GPS:${gps_number || 0} RTK:${rtk_number || 0}`;
    },

    // 获取GPS提示文本
    getGpsTooltip(droneOsd) {
      const positionState = droneOsd.position_state;
      if (!positionState) return 'GPS状态未知';

      const {
        gps_number,
        rtk_number,
        quality,
        is_calibration,
        is_fixed
      } = positionState;

      const qualityMap = {
        1: '1档', 2: '2档', 3: '3档', 4: '4档', 5: '5档', 10: 'RTK fixed'
      };

      const calibrationText = is_calibration === 1 ? '已标定' : '未标定';
      const fixedMap = {
        0: '未开始', 1: '收敛中', 2: '收敛成功', 3: '收敛失败'
      };
      const fixedText = fixedMap[is_fixed] || '未知';

      return `GPS搜星: ${gps_number || 0}颗\nRTK搜星: ${rtk_number || 0}颗\n搜星档位: ${qualityMap[quality] || '未知'}\n标定状态: ${calibrationText}\n收敛状态: ${fixedText}`;
    },

    // 获取网络图标
    getNetworkIcon(networkState) {
      if (!networkState) return 'xh_0'; // 无网络状态时显示无信号

      const { quality } = networkState;

      // 根据网络质量显示对应的信号强度图标
      // quality: 0-无信号, 1-差, 2-较差, 3-一般, 4-较好, 5-好
      switch (quality) {
        case 0: return 'xh_0'; // 无信号
        case 1: return 'xh_1'; // 差
        case 2: return 'xh_2'; // 较差
        case 3: return 'xh_3'; // 一般
        case 4: return 'xh_4'; // 较好
        case 5: return 'xh_5'; // 好
        default: return 'xh_0'; // 默认无信号
      }
    },

    // 获取信号类型文本
    getlinkWorkmodeTypeText(linkWorkmode) {
      const typeMap = { 0: 'SDR', 1: '4G' };
      return typeMap[linkWorkmode] || '';
    },


    // 获取网络显示文本
    getNetworkDisplayText(networkState) {
      if (!networkState) return '--';

      const { type, quality, rate } = networkState;

      const typeMap = { 1: '4G', 2: '以太网' };
      const qualityMap = { 0: '无信号', 1: '差', 2: '较差', 3: '一般', 4: '较好', 5: '好' };

      return `${typeMap[type] || '未知'} ${qualityMap[quality] || '--'}`;
    },

    // 获取网络提示文本
    getNetworkTooltip(droneOsd) {
      const networkState = droneOsd.network_state;
      if (!networkState) return '网络状态未知';

      const { type, quality, rate } = networkState;

      const typeMap = { 1: '4G', 2: '以太网' };
      const qualityMap = { 0: '无信号', 1: '差', 2: '较差', 3: '一般', 4: '较好', 5: '好' };

      return `网络类型: ${typeMap[type] || '未知'}\n网络质量: ${qualityMap[quality] || '未知'}\n网络速率: ${rate ? rate.toFixed(1) + ' KB/s' : '--'}`;
    },




    // 绘制圆形区域和多边形区域的函数
    drawRegions(regions, map) {
      // 首先按面积大小对区域进行排序
      const sortedAreas = [...regions.data.areas].sort((a, b) => {
        // 对于圆形区域，比较半径
        if (a.shape === 0 && b.shape === 0) {
          return b.radius - a.radius; // 大区域在前
        }
        // 对于多边形区域，可以根据点的数量或其他属性排序
        return 0;
      });

      // 按照从大到小的顺序绘制区域，小区域的 zIndex 更高
      sortedAreas.forEach((region2, index) => {
        // 计算 zIndex，小区域有更高的 zIndex
        const baseZIndex = 50;
        const zIndexValue = baseZIndex + index * 10; // 每个区域增加10的zIndex

        if (region2.shape === 0) {
          const gcj02Coord = this.wgs84ToGcj02(region2.lng, region2.lat);
          region2.lng = gcj02Coord.lng;
          region2.lat = gcj02Coord.lat;
          const circle = new AMap.Circle({
            center: [region2.lng, region2.lat],
            radius: region2.radius,
            strokeColor: region2.color,
            strokeWeight: 2,
            fillColor: region2.color,
            fillOpacity: 0.35,
            zIndex: zIndexValue, // 使用计算的 zIndex
            cursor: 'pointer',
            extData: { // 存储区域信息，用于点击事件
              name: region2.name,
              level: region2.level,
              color: region2.color,
              height: region2.height
            }
          });
          circle.setMap(map);

          // 添加点击事件
          circle.on('click', (e) => {
            // 使用存储在 extData 中的信息
            const data = e.target.getExtData();
            // 更新信息窗体内容
            this.selectedRegion = { name: data.name };
            this.infoWindow.setContent(this.createInfoWindowContent(
              data.name,
              data.level,
              data.color,
              data.height
            ));
            // 在点击位置打开信息窗体
            this.infoWindow.open(map, e.lnglat);

            // 阻止事件冒泡 - 使用正确的方法
            e.stopPropagation && e.stopPropagation(); // 如果存在则调用
            // 或者直接返回 false 也可以阻止冒泡
            return false;
          });
        } else if (region2.shape === 2) {  // 多边形区域
          region2.sub_areas.forEach((region3, subIndex) => {
            // 子区域的 zIndex 应该更高
            const subZIndex = zIndexValue - subIndex - 1;

            if (region3.polygon_points && region3.shape === 1) {
              region3.polygon_points[0].forEach((zb) => {
                const gcj02Coord = this.wgs84ToGcj02(zb[0], zb[1]);
                zb[0] = gcj02Coord.lng;
                zb[1] = gcj02Coord.lat;
              });

              const polygon = new AMap.Polygon({
                path: region3.polygon_points[0],
                strokeColor: region3.color,
                strokeWeight: 1,
                fillColor: region3.color,
                fillOpacity: 0.35,
                zIndex: subZIndex, // 使用更高的 zIndex
                cursor: 'pointer',
                extData: { // 存储区域信息
                  name: region3.name || region2.name, // 优先使用子区域名称
                  level: region3.level || region2.level,
                  color: region3.color,
                  height: region3.height
                }
              });
              polygon.setMap(map);

              // 添加点击事件
              polygon.on('click', (e) => {
         
                // 使用存储在 extData 中的信息
                const data = e.target.getExtData();
                // 更新信息窗体内容
                this.selectedRegion = { name: data.name };
                this.infoWindow.setContent(this.createInfoWindowContent(
                  data.name,
                  data.level,
                  data.color,
                  data.height
                ));
                // 在点击位置打开信息窗体
                this.infoWindow.open(map, e.lnglat);

                // 阻止事件冒泡
                e.stopPropagation && e.stopPropagation(); // 如果存在则调用
                // 或者直接返回 false 也可以阻止冒泡
                return false;
              });
            } else {
               const gcj02Coord = this.wgs84ToGcj02(region2.lng, region2.lat);
              const polygon1 = new AMap.Circle({
                center: [gcj02Coord.lng, gcj02Coord.lat],
                radius: region2.radius,
                strokeColor: region2.color,
                strokeWeight: 2,
                fillColor: region2.color,
                fillOpacity: 0.35,
                zIndex: subZIndex,
                cursor: 'pointer',
                extData: {
                  name: region3.name || region2.name,
                  level: region3.level || region2.level,
                  color: region3.color,
                  height: region3.height
                }
              });
              polygon1.setMap(map);

              polygon1.on('click', (e) => {
        
                const data = e.target.getExtData();
                // 更新信息窗体内容
                this.selectedRegion = { name: data.name };
                this.infoWindow.setContent(this.createInfoWindowContent(
                  data.name,
                  data.level,
                  data.color,
                  data.height
                ));
                // 在点击位置打开信息窗体
                this.infoWindow.open(map, e.lnglat);

                // 阻止事件冒泡
                e.stopPropagation && e.stopPropagation(); // 如果存在则调用
                // 或者直接返回 false 也可以阻止冒泡
                return false;
              });
            }
          });
        }
      });
    },

    // 获取机场信息
    getAirportInfo(deviceSn) {
      // 如果有真实的OSD数据，返回真实数据
      if (this.osdDock[deviceSn]) {
        return this.osdDock[deviceSn];
      }

      //没有数据返回
      return {
        temperature: 0,
        humidity: 0,
        wind_speed: 0,
        rainfall: 0,
        storage: {
          total: 0,
          used: 0
        }
      };
    },

    // 格式化温度
    formatTemperature(temperature) {
      if (temperature === undefined || temperature === null) return '--';
      return `${temperature.toFixed(1)}°C`;
    },

    // 格式化湿度
    formatHumidity(humidity) {
      if (humidity === undefined || humidity === null) return '--';
      return `${humidity.toFixed(1)}%`;
    },

    // 格式化风速
    formatWindSpeed(windSpeed) {
      if (windSpeed === undefined || windSpeed === null) return '--';
      return `${windSpeed.toFixed(1)}m/s`;
    },

    // 格式化降雨量
    formatRainfall(rainfall) {
      if (rainfall === undefined || rainfall === null) return '--';
      const rainfallMap = {
        0: '无雨',
        1: '小雨',
        2: '中雨',
        3: '大雨'
      };
      return rainfallMap[rainfall] || '未知';
    },

    // 格式化存储容量
    formatStorage(storage) {
      if (!storage || storage.total === undefined || storage.used === undefined) return '--';
      const totalMB = (storage.total / 1024).toFixed(1);
      const usedMB = (storage.used / 1024).toFixed(1);
      const usagePercent = ((storage.used / storage.total) * 100).toFixed(1);
      return `${usedMB}/${totalMB}MB (${usagePercent}%)`;
    },

    // 处理设备操作（重启、格式化）
    async handleDeviceAction(deviceSn, action) {
      if (!deviceSn || !action) {
        console.error('设备SN或操作类型缺失');
        return;
      }

      // 根据操作类型显示不同的确认信息
      let confirmMessage = '';
      let actionName = '';

      switch (action) {
        case 'device_reboot':
          confirmMessage = '确定要重启机场吗？重启过程中设备将暂时不可用。';
          actionName = '重启机场';
          break;
        case 'device_format':
          confirmMessage = '确定要格式化机场吗？此操作将清除所有数据且不可恢复！';
          actionName = '格式化机场';
          break;
        case 'drone_format':
          confirmMessage = '确定要格式化无人机吗？此操作将清除所有数据且不可恢复！';
          actionName = '格式化无人机';
          break;
        default:
          console.error('未知的操作类型:', action);
          return;
      }

      try {
        // 显示确认对话框
        await this.$confirm(confirmMessage, '操作确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        console.log(`执行${actionName}操作:`, { deviceSn, action });

        // 调用设备控制接口
        const response = await sendDebugCommand(deviceSn, action, { action: 1 });

        if (response.code === 0) {
          this.$message.success(`${actionName}指令已发送`);
        } else {
          throw new Error(response.message || `${actionName}失败`);
        }
      } catch (error) {
        if (error === 'cancel') {
          console.log('用户取消操作');
          return;
        }
        console.error(`${actionName}失败:`, error);
        this.$message.error(`${actionName}失败: ${error.message || '网络错误'}`);
      }
    },

    // 切换测距工具
    toggleMeasureDistance() {
      if (this.currentMapTool === 'distance') {
        this.disableMeasureDistance();
      } else {
        this.enableMeasureDistance();
      }
    },

    // 启用测距工具 (API 2.0 优化版本)
    enableMeasureDistance() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.warning('地图未初始化');
        return;
      }
      // 先禁用其他工具
      this.disableMeasureArea();
      this.currentMapTool = 'distance';

      // 使用 API 2.0 的异步插件加载方式
      AMap.plugin(['AMap.RangingTool'], () => {
        try {
          this.distanceTool = new AMap.RangingTool(this.$refs.amap.map, {
            // 线条样式配置 (API 2.0 优化)
            strokeStyle: 'solid',
            strokeColor: '#1890ff',
            strokeOpacity: 0.8,
            strokeWeight: 3,
            strokeDasharray: [5, 5], // 虚线样式

            // 填充样式配置
            fillColor: '#1890ff',
            fillOpacity: 0.1,

            // 起点标记配置 - 使用圆点样式
            startMarkerOptions: {
              content: '<div class="measure-point measure-point-start"></div>',
              offset: new AMap.Pixel(0, 0),
              anchor: 'center'
            },

            // 终点标记配置 - 使用圆点样式
            endMarkerOptions: {
              content: '<div class="measure-point measure-point-end"></div>',
              offset: new AMap.Pixel(0, 0),
              anchor: 'center'
            },

            // 中间点标记配置 - 使用圆点样式
            midMarkerOptions: {
              content: '<div class="measure-point measure-point-mid"></div>',
              offset: new AMap.Pixel(0, 0),
              anchor: 'center'
            }
          });

          // 绑定事件监听器
          this.bindDistanceToolEvents();

          // 开启测距工具
          this.distanceTool.turnOn();

          this.$message.info('测距工具已启用，点击地图开始测距');

        } catch (error) {
          console.error('测距工具初始化失败:', error);
          this.$message.error('测距工具启动失败');
          this.currentMapTool = null;
        }
      });
    },

    // 绑定测距工具事件 (API 2.0 优化)
    bindDistanceToolEvents() {
      if (!this.distanceTool) return;

      // 测距开始事件
      this.distanceTool.on('start', () => {
        console.log('开始测距');
      });

      // 测距进行中事件
      this.distanceTool.on('addnode', (event) => {
        console.log('添加测距点:', event);
      });

      // 测距完成事件
      this.distanceTool.on('end', (event) => {
        const distance = event.distance;

        // 保存测量结果
        this.measurementResults.push({
          type: 'distance',
          value: distance,
          text: this.formatDistance(distance),
          timestamp: new Date().toLocaleString(),
          path: event.path || []
        });

        this.$message.success(`测距完成：${this.formatDistance(distance)}`);
        console.log('测距完成:', { distance, path: event.path });
      });

      // 错误处理事件
      this.distanceTool.on('error', (error) => {
        console.error('测距工具错误:', error);
        this.$message.error('测距过程中发生错误');
      });
    },

    // 禁用测距工具 (API 2.0 优化)
    disableMeasureDistance() {
      if (this.distanceTool) {
        try {
          // 移除所有事件监听器
          this.distanceTool.off('start');
          this.distanceTool.off('addnode');
          this.distanceTool.off('end');
          this.distanceTool.off('error');

          // 关闭工具
          this.distanceTool.turnOff();

          // 清除工具实例
          this.distanceTool = null;

          console.log('测距工具已禁用');
        } catch (error) {
          console.warn('禁用测距工具时出错:', error);
        }
      }
      if (this.currentMapTool === 'distance') {
        this.currentMapTool = null;
      }
    },

    // 切换测面积工具
    toggleMeasureArea() {
      if (this.currentMapTool === 'area') {
        this.disableMeasureArea();
      } else {
        this.enableMeasureArea();
      }
    },

    // 启用测面积工具 (API 2.0 优化版本)
    enableMeasureArea() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.warning('地图未初始化');
        return;
      }
      // 先禁用其他工具
      this.disableMeasureDistance();
      this.currentMapTool = 'area';

      // 使用 API 2.0 的异步插件加载方式
      AMap.plugin(['AMap.MouseTool'], () => {
        try {
          this.areaTool = new AMap.MouseTool(this.$refs.amap.map);

          // 开启绘制多边形模式 (API 2.0 优化样式)
          this.areaTool.polygon({
            strokeColor: '#52c41a',
            strokeWeight: 3,
            strokeOpacity: 0.8,
            strokeStyle: 'solid',
            strokeDasharray: [10, 5], // 虚线样式
            fillColor: '#52c41a',
            fillOpacity: 0.2,
            // API 2.0 新增配置
            cursor: 'crosshair',
            bubble: true
          });

          // 绑定测面积事件
          this.bindAreaToolEvents();

          this.$message.info('测面积工具已启用，点击地图开始绘制多边形');

        } catch (error) {
          console.error('测面积工具初始化失败:', error);
          this.$message.error('测面积工具启动失败');
          this.currentMapTool = null;
        }
      });
    },

    // 绑定测面积工具事件 (API 2.0 优化)
    bindAreaToolEvents() {
      if (!this.areaTool) return;

      // 绘制开始事件
      this.areaTool.on('draw-start', () => {
        console.log('开始绘制面积');
      });

      // 绘制进行中事件
      this.areaTool.on('draw-move', (event) => {
        console.log('绘制移动:', event);
      });

      // 绘制完成事件
      this.areaTool.on('draw', (event) => {
        const polygon = event.obj;
        const path = polygon.getPath();
        // 使用 API 2.0 的几何计算方法
        const area = AMap.GeometryUtil.ringArea(path);
        // 保存测量结果
        this.measurementResults.push({
          type: 'area',
          value: area,
          text: this.formatArea(area),
          timestamp: new Date().toLocaleString(),
          path: path,
          polygon: polygon
        });
        // 添加顶点标记
        this.addAreaVertexMarkers(path);
        // 添加面积标签
        this.addAreaLabel(polygon, area);
        this.$message.success(`测面积完成：${this.formatArea(area)}`);
        console.log('测面积完成:', { area, path });
      });

      // 错误处理事件
      this.areaTool.on('error', (error) => {
        console.error('测面积工具错误:', error);
        this.$message.error('测面积过程中发生错误');
      });
    },

    // 添加面积顶点标记 (API 2.0 优化)
    addAreaVertexMarkers(path) {
      path.forEach((point) => {
        const marker = new AMap.Marker({
          position: point,
          content: '<div class="measure-point measure-point-area"></div>',
          offset: new AMap.Pixel(0 ,0),
          anchor: 'center',
          zIndex: 100
        });
        marker.setMap(this.$refs.amap.map);
      });
    },

    // 添加面积标签 (API 2.0 优化)
    addAreaLabel(polygon, area) {
      const bounds = polygon.getBounds();
      const center = bounds.getCenter();
      const labelMarker = new AMap.Marker({
        position: center,
        content: `<div class="measure-area-label">${this.formatArea(area)}</div>`,
        offset: new AMap.Pixel(-50, -10),
        anchor: 'center',
        zIndex: 101
      });
      labelMarker.setMap(this.$refs.amap.map);
    },

    // 禁用测面积工具 (API 2.0 优化)
    disableMeasureArea() {
      if (this.areaTool) {
        try {
          // 移除所有事件监听器
          this.areaTool.off('draw-start');
          this.areaTool.off('draw-move');
          this.areaTool.off('draw');
          this.areaTool.off('error');
          // 关闭工具
          this.areaTool.close(true);
          // 清除工具实例
          this.areaTool = null;
          console.log('测面积工具已禁用');
        } catch (error) {
          console.warn('禁用测面积工具时出错:', error);
        }
      }
      if (this.currentMapTool === 'area') {
        this.currentMapTool = null;
      }
    },

    // 清除所有测量结果
    clearMeasurements() {
      // 清除测距工具的结果
      if (this.distanceTool) {
        this.distanceTool.turnOff(true);
      }

      // 清除测面积工具的结果
      if (this.areaTool) {true
        this.areaTool.close(true);
      }

      // 清除地图上的测量相关覆盖物（保留设备标记和禁飞区）
      if (this.$refs.amap && this.$refs.amap.map) {
        const overlays = this.$refs.amap.map.getAllOverlays();
        overlays.forEach(overlay => {
          let shouldRemove = false;

          // 检查是否是测量点标记
          if (overlay.getContent) {
            const content = overlay.getContent();
            if (typeof content === 'string' &&
                (content.includes('measure-point') ||
                 content.includes('measure-area-label'))) {
              shouldRemove = true;
            }
          }

          // 如果不是测量点标记，进一步检查其他类型
          if (!shouldRemove && overlay.CLASS_NAME) {
            const className = overlay.CLASS_NAME;
            const extData = overlay.getExtData && overlay.getExtData();

            if (className.includes('Polyline') ||
                className.includes('Polygon') ||
                className.includes('Marker')) {

              // 保留设备相关的覆盖物
              if (extData && extData.deviceSn) {
                shouldRemove = false;
              }
              // 保留禁飞区相关的覆盖物
              else if (extData && (extData.name || extData.level || extData.color || extData.type)) {
                shouldRemove = false;
              }
              // 其他没有重要extData的覆盖物，认为是测量相关的
              else {
                shouldRemove = true;
              }
            }
          }

          // 移除测量相关的覆盖物
          if (shouldRemove) {
            this.$refs.amap.map.remove(overlay);
          }
        });
      }

      // 重置工具状态
      this.currentMapTool = null;
      this.distanceTool = null;
      this.areaTool = null;
      this.measurementResults = [];

      this.$message.success('已清除所有测量结果');
    },



    // 格式化距离显示
    formatDistance(distance) {
      if (distance < 1000) {
        return `${distance.toFixed(2)}米`;
      } else {
        return `${(distance / 1000).toFixed(2)}公里`;
      }
    },

    // 格式化面积显示
    formatArea(area) {
      if (area < 10000) {
        return `${area.toFixed(2)}平方米`;
      } else {
        return `${(area / 10000).toFixed(2)}公顷`;
      }
    },

    // API 2.0 优化：切换路况图层
    async toggleTrafficLayer() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.warning('地图未初始化');
        return;
      }

      const map = this.$refs.amap.map;

      if (this.showTrafficLayer) {
        // 关闭路况图层
        if (this.trafficLayer) {
          map.remove(this.trafficLayer);
          this.trafficLayer = null;
        }
        this.showTrafficLayer = false;
        this.$message.info('路况信息已关闭');
      } else {
        try {
          // API 2.0 异步插件加载
          await new Promise((resolve) => {
            AMap.plugin(['AMap.TileLayer.Traffic'], resolve);
          });

          this.trafficLayer = new AMap.TileLayer.Traffic({
            zIndex: 10,
            opacity: 0.8,
            // API 2.0 新增配置
            autoRefresh: true,
            interval: 180, // 3分钟刷新一次
            detectRetina: true
          });

          map.add(this.trafficLayer);
          this.showTrafficLayer = true;
          this.$message.success('路况信息已开启');

          // API 2.0 新增：监听图层加载事件
          this.trafficLayer.on('complete', () => {
            console.log('路况图层加载完成');
          });

        } catch (error) {
          console.error('路况图层加载失败:', error);
          this.$message.error('路况信息加载失败');
        }
      }
    },

    // API 2.0 优化：切换卫星图层
    async toggleSatelliteLayer() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.warning('地图未初始化');
        return;
      }

      const map = this.$refs.amap.map;

      if (this.showSatelliteLayer) {
        // 关闭卫星图层
        if (this.satelliteLayer) {
          map.remove(this.satelliteLayer);
          this.satelliteLayer = null;
        }
        this.showSatelliteLayer = false;
        this.$message.info('已切换到标准地图');
      } else {
        try {
          // API 2.0 优化：使用地图样式切换（推荐方式）
          map.setMapStyle('amap://styles/satellite');
          this.showSatelliteLayer = true;
          this.$message.success('已切换到卫星图');

          // 监听样式切换完成
          map.on('complete', () => {
            console.log('卫星图样式切换完成');
          });

        } catch (error) {
          console.error('卫星图样式切换失败，尝试图层方式:', error);

          // 备用方案：使用卫星图层
          try {
            await new Promise((resolve) => {
              AMap.plugin(['AMap.TileLayer.Satellite'], resolve);
            });

            this.satelliteLayer = new AMap.TileLayer.Satellite({
              zIndex: 1,
              opacity: 1,
              // API 2.0 新增配置
              detectRetina: true,
              reuseTile: true
            });

            map.add(this.satelliteLayer);
            this.showSatelliteLayer = true;
            this.$message.success('已切换到卫星图');

            // 监听图层加载事件
            this.satelliteLayer.on('complete', () => {
              console.log('卫星图层加载完成');
            });

          } catch (layerError) {
            console.error('卫星图层加载失败，使用自定义瓦片:', layerError);

            // 最后备用方案：自定义瓦片图层
            try {
              this.satelliteLayer = new AMap.TileLayer({
                tileUrl: 'https://webst0{1,2,3,4}.is.autonavi.com/appmaptile?style=6&x=[x]&y=[y]&z=[z]',
                zIndex: 1,
                opacity: 1,
                // API 2.0 优化配置
                detectRetina: true,
                reuseTile: true,
                tileSize: 256
              });

              map.add(this.satelliteLayer);
              this.showSatelliteLayer = true;
              this.$message.success('已切换到卫星图');

            } catch (fallbackError) {
              console.error('所有卫星图方案都失败:', fallbackError);
              this.$message.error('卫星图加载失败，请稍后重试');
            }
          }
        }
      }
    },

    // API 2.0 优化：清理地图图层
    cleanupMapLayers() {
      const map = this.$refs.amap?.map;
      if (!map) return;

      try {
        // 清理路况图层
        if (this.trafficLayer) {
          // API 2.0 移除事件监听器
          this.trafficLayer.off('complete');
          map.remove(this.trafficLayer);
          this.trafficLayer = null;
        }

        // 清理卫星图层
        if (this.satelliteLayer) {
          // API 2.0 移除事件监听器
          this.satelliteLayer.off('complete');
          map.remove(this.satelliteLayer);
          this.satelliteLayer = null;
        }

        // API 2.0 重置地图样式为标准地图
        if (this.showSatelliteLayer) {
          try {
            map.setMapStyle('amap://styles/normal');
          } catch (error) {
            console.warn('重置地图样式失败:', error);
          }
        }

        // 清理测量工具
        this.disableMeasureDistance();
        this.disableMeasureArea();

        // 重置状态
        this.showTrafficLayer = false;
        this.showSatelliteLayer = false;
        this.currentMapTool = null;
        this.measurementResults = [];

        console.log('地图图层清理完成 (API 2.0)');

      } catch (error) {
        console.error('清理地图图层失败:', error);
      }
    }
  }
}
</script>

<style>
/* 使用 rem 作为基础单位 */
:root {
  --sidebar-width: 17%;
  --card-padding: 1rem;
  --font-size-base: 1rem;
  --icon-size: 2rem;
}

.dockstate {
  justify-content: center;
  align-items: center;
  display: flex;

}


.flight-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
}

.map-wrapper {
  flex: 1;
  width: calc(100% - var(--sidebar-width));
  height: 100%;
  position: relative;
}

.flight-sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
}

.flight-sidebar-header {
  height: 3.125rem;
  line-height: 3.125rem;
  padding: 0 0.9375rem;
  font-size: var(--font-size-base);
  font-weight: bold;
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
  color: #303133;
}

.flight-sidebar-header .sidebar-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
  vertical-align: middle;
}

.flight-airport-list {
  flex: 1;
  overflow-y: auto;
  padding: calc(var(--card-padding) * 0.625);
}

.flight-airport-item {
  margin-bottom: calc(var(--card-padding) * 0.625);
  border-radius: 1.25rem;
  position: relative;
  cursor: pointer;
}

.flight-airport-item .el-card__body {
  padding: calc(var(--card-padding) * 0.625);
  height: 9rem;
}

.airport-title {
  margin-bottom: calc(var(--card-padding) * 0.2);
  display: flex;
  align-items: center;
}

.airport-divider {
  height: 1px;
  background-color: #EBEEF5;
  margin: 1vh 0;
}

.airport-image {
  width: var(--icon-size);
  height: var(--icon-size);
  text-align: center;
}

.airport-img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.airport-actions {
  margin-top: 10px;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
  text-align: right;
}

.airport-actions .el-button:not(.map-tool-btn) {
  margin-left: 10px;
}

.amap-logo {
  display: none !important;
}

.amap-copyright {
  display: none !important;
}

/* Element UI 组件样式优化 */
.el-descriptions-item__label {
  color: #606266;
}

.el-descriptions-item__content {
  color: #303133;
}

.el-card {
  border: 1px solid #bac3d7;
}

.el-card.is-hover-shadow:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.locate-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: none;
}

.flight-airport-item:hover .locate-button {
  display: block;
}

.video-containers-wrapper {
  position: absolute;
  top: 20px;
  left: 20px;
  max-height: calc(100vh - 40px);
  max-width: calc(100% - var(--sidebar-width) - 40px);
  z-index: 2001;
  display: flex;
  flex-flow: column wrap;
  align-content: flex-start;
  gap: 20px;
  overflow-x: auto;
  overflow-y: hidden;
  pointer-events: none;
  /* 允许点击穿透到地图 */
}

.video-container-wrapper {
  display: flex;
  align-items: flex-start;
  pointer-events: auto;
}

.video-container {
  position: relative;
  flex: 0 0 auto;
  width: 400px;
  height: auto;
  pointer-events: auto;
  /* 视频容器可以交互 */
}

/* 顶部控件容器样式 */
.video-top-controls {
  position: absolute;
  top: 8px;
  right: 45px;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.debug-btn {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 4px;
}

/* 调试面板样式 */
.debug-panel {
  width: 400px;
  margin-left: 10px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  max-height: 380px;
  display: flex;
  flex-direction: column;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
}

.debug-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.debug-close-btn {
  color: #909399;
  padding: 0;
  font-size: 16px;
}

.debug-close-btn:hover {
  color: #f56c6c;
}

.debug-panel-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  max-height: 300px;
}

.debug-loading,
.debug-error,
.debug-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: #909399;
}

.debug-loading i,
.debug-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.debug-error {
  color: #f56c6c;
}

.debug-info {
  font-size: 12px;
}

.debug-sections-horizontal {
  display: flex;
  gap: 15px;
  height: 100%;
}

.debug-section-left,
.debug-section-right {
  flex: 1;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
}

.debug-section-title {
  background: #e9ecef;
  padding: 8px 12px;
  font-weight: 500;
  color: #495057;
  font-size: 13px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
}

.debug-control-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  margin: 0;
}

.debug-control-item:last-child {
  border-bottom: none;
}

.debug-control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.debug-control-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 0;
}

.debug-control-widget {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

/* 确保TCPlayerVideo组件正确显示 */
.video-container>>>.tcplayer-container {
  width: 400px;
  height: auto;
}

.video-container>>>.video-wrapper {
  height: 250px;
}

.video-container>>>video {
  width: 400px !important;
  height: 250px !important;
}

/* 美化水平滚动条 */
.video-containers-wrapper::-webkit-scrollbar {
  height: 6px;
}

.video-containers-wrapper::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.video-containers-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

/* 机场信息展示区域样式 */
.airport-info-panel {
  width: 400px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
  backdrop-filter: blur(10px);
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: space-between;
}

.info-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px 12px;
  min-width: 80px;
  flex: 1;
  max-width: calc(50% - 4px);
}

.info-icon {
  margin-right: 8px;
  font-size: 16px;
  width: 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-value {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 响应式布局 */
@media screen and (max-width: 1920px) {
  :root {
    --sidebar-width: 17%;
    --card-padding: 1rem;
    --font-size-base: 1rem;
    --icon-size: 2rem;
  }
}

@media screen and (max-width: 1600px) {
  :root {
    --sidebar-width: 20%;
    --card-padding: 0.9rem;
    --font-size-base: 0.95rem;
    --icon-size: 1.8rem;
  }
}

@media screen and (max-width: 1366px) {
  :root {
    --sidebar-width: 22%;
    --card-padding: 0.8rem;
    --font-size-base: 0.9rem;
    --icon-size: 1.6rem;
  }

  .task-name {
    font-size: 0.9rem;

  }
}

@media screen and (max-width: 1280px) {
  :root {
    --sidebar-width: 25%;
    --card-padding: 0.7rem;
    --font-size-base: 0.85rem;
    --icon-size: 1.5rem;
  }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  :root {
    --sidebar-width: 100%;
    --card-padding: 0.6rem;
    --font-size-base: 0.8rem;
    --icon-size: 1.4rem;
  }

  .flight-container {
    flex-direction: column;
  }

  .map-wrapper {
    width: 100%;
    height: 50vh;
  }

  .flight-sidebar {
    width: 100%;
    height: 50vh;
  }
}

.airport-name {
  padding-left: calc(var(--card-padding) * 0.625);
  font-size: var(--font-size-base);
}

.task-name {

  padding-left: 0.3rem;
  line-height: 1.7rem;
  font-size: var(--font-size-base);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: calc(var(--card-padding) * 0.625);
  width: 100%;
}

/* 状态行高度自适应 */
.status-row {
  min-height: 2.5rem;
  padding: calc(var(--card-padding) * 0.3) 0;
}





.el-dialog__wrapper {
  position: fixed !important;
  top: 0;
  left: 0;
  z-index: 2001;
}

.el-dialog {
  margin: 0 !important;
}

/* 第一个对话框位置 */
.left-dialog {
  position: absolute;
  left: 20px !important;
  top: 20px !important;
}

/* 第二个对话框位置 */
.second-dialog {
  position: absolute;
  left: 20px !important;
  top: calc(50% + 20px) !important;
}

/* 添加高亮动画效果 */
@keyframes highlight {
  0% {
    box-shadow: 0 0 0 2px #409EFF;
  }

  100% {
    box-shadow: none;
  }
}

.highlight {
  animation: highlight 1s ease-in-out;
}

/* 自定义信息窗体样式 */
.custom-info-window {
  padding: 12px 15px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-width: 180px;
  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
}

.info-area-title,
.info-level {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.info-label {
  font-weight: bold;
  color: #666;
  margin-right: 5px;
}

.warning-level {
  color: #f56c6c;
  font-weight: 500;
}

/* 隐藏高德地图默认的信息窗体关闭按钮 */
.amap-info-close {
  display: none !important;
}

/* 调整信息窗体箭头样式 */
.amap-info-sharp {
  border-top: 8px solid white !important;
}

/* 区域类型样式 */
.no-fly-zone {
  color: #DE4329;
  font-weight: 500;
}

.height-restricted-zone {
  color: #979797;
  font-weight: 500;
}

.warning-zone {
  color: #ee8815;
  font-weight: 500;
}

.height-value {
  color: #409EFF;
  font-weight: 500;
}

.info-height {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}



/* 无人机状态图标容器 */
.drone-status-icons {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

/* 状态图标通用样式 */
.status-icon {
  display: flex;
  align-items: center;
  gap: 0px;
  color: rgb(0, 0, 0);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 11px;
  white-space: nowrap;
  min-width: 0;
  background: rgba(3, 3, 3, 0);
  backdrop-filter: blur(2px);
}

/* 电池图标样式 */
.battery-icon.charging .battery-svg-icon {
  color: #07ff51;
  /* 充电时只有图标变绿色 */
}

.battery-icon.low .battery-svg-icon {
  color: #f44336;
  /* 低电量时只有图标变红色 */
}

/* 双电池样式 */
.battery-icon.dual-battery {
  flex-direction: row;
  align-items: center;
}

.dual-battery-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-size: 10px;
}

.dual-battery-text .battery-percent {
  margin: 0;
  padding: 0;
}

.battery-percent {
  font-weight: bold;
  font-size: 13px;
  margin-left: -5px;
  margin-top: 2px;
  color: rgb(0, 0, 0);
  /* 文字保持黑色 */
}

.battery-svg-icon {
  width: 16px;
  height: 16px;
  font-size: 50px !important;
  margin: -10px;
}

/* GPS图标样式 */
.gps-icon .el-icon-location {
  font-size: 16px;
}

.gps-info-container {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  line-height: 1.2;
}

.gps-line,
.rtk-line {
  font-size: 13px;
}

/* 网络图标样式 */
.network-icon-container {
  position: relative;
  display: inline-block;
}

.network-svg-icon {
  width: 16px;
  height: 16px;
  font-size: 40px;
}

.network-type-label {
  position: absolute;
  top: -3px;
  left: -6px;
  font-size: 12px;

  color: rgb(0, 0, 0);
  background: rgba(255, 255, 255, 0.8);
  padding: 1px 2px;
  border-radius: 2px;
  line-height: 1;
  white-space: nowrap;
  z-index: 1;
}

.network-info {
  font-size: 13px;
}

/* 调试面板样式 */
.debug-panel {
  width: 400px;
  margin-left: 10px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  max-height: 380px;
  display: flex;
  flex-direction: column;
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
}

.debug-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.debug-close-btn {
  color: #909399;
  padding: 0;
  font-size: 16px;
}

.debug-close-btn:hover {
  color: #f56c6c;
}

.debug-panel-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  max-height: 300px;
}

.debug-loading,
.debug-error,
.debug-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: #909399;
}

.debug-loading i,
.debug-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.debug-error {
  color: #f56c6c;
}

.debug-info {
  font-size: 12px;
}

.debug-sections-horizontal {
  display: flex;
  gap: 15px;
  height: 100%;
}

.debug-section-left,
.debug-section-right {
  flex: 1;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
}

.debug-section-title {
  background: #e9ecef;
  padding: 8px 12px;
  font-weight: 500;
  color: #495057;
  font-size: 13px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.debug-section-text {
  flex: 1;
  text-align: center;
}

.debug-action-btn {
  padding: 2px 8px !important;
  font-size: 11px !important;
  height: 24px !important;
  line-height: 1 !important;
  border-radius: 3px !important;
  min-width: 50px;
}

.debug-action-btn.el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
}

.debug-action-btn.el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.debug-action-btn:hover {
  opacity: 0.8;
}

.debug-control-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  margin: 0;
}

.debug-control-item:last-child {
  border-bottom: none;
}

.debug-control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.debug-control-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 0;
}

.debug-control-widget {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  width: 60%;
}

.debug-control-widget .el-switch {
  margin: 0;
}

.debug-control-widget .el-select {
  width: 100%;
}

.debug-item {
  display: flex;
  margin-bottom: 8px;
  word-break: break-all;
}

.debug-key {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.debug-value {
  color: #303133;
  flex: 1;
  white-space: pre-wrap;
}

/* OSD数据显示样式 */
.osd-debug-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 400px;
  max-height: 500px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  border-radius: 8px;
  padding: 15px;
  z-index: 1000;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
}

.osd-debug-panel h3 {
  margin: 0 0 15px 0;
  color: #00ff00;
  font-size: 16px;
  border-bottom: 1px solid #333;
  padding-bottom: 5px;
}

.osd-device-data {
  margin-bottom: 20px;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
}

.osd-device-data h4 {
  margin: 0 0 10px 0;
  color: #ffff00;
  font-size: 14px;
}

.osd-data-content {
  max-height: 200px;
  overflow-y: auto;
}

.osd-data-content pre {
  margin: 0;
  font-size: 11px;
  line-height: 1.4;
  color: #ccc;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 测试按钮样式 */
.test-buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

/* WebSocket状态显示样式 */
.websocket-status {
  position: fixed;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 10px 15px;
  border-radius: 6px;
  z-index: 1000;
  font-size: 12px;
  min-width: 150px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.connected .status-dot {
  background-color: #00ff00;
  box-shadow: 0 0 6px #00ff00;
}

.status-indicator.connecting .status-dot {
  background-color: #ffff00;
  animation: pulse 1s infinite;
}

.status-indicator.error .status-dot {
  background-color: #ff0000;
}

.status-indicator.disconnected .status-dot {
  background-color: #666;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

.error-message {
  margin-top: 5px;
  color: #ff6b6b;
  font-size: 11px;
  word-break: break-word;
}

/* 控制按钮区域样式 */
.control-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

/* Mode Code 显示样式 */
.mode-code-display {
  display: flex;
  align-items: center;
}

.mode-code-badge {
  display: flex;
  align-items: center;
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  gap: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mode-code-badge:hover {
  background: #337ecc;
}

.mode-code-label {
  font-size: 11px;
  opacity: 0.9;
}

.mode-code-value {
  font-weight: 600;
}

/* 无人机信息面板样式 */
.drone-info-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 0 0 6px 6px;
}

.drone-info-panel .info-grid {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 12px;
}

.drone-info-panel .info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: white;
}

.drone-info-panel .info-icon {
  font-size: 14px;
  opacity: 0.9;
}

.drone-info-panel .info-value {
  font-weight: 500;
  color: #fff;
}

/* 调整机场信息面板样式以区分 */
.airport-info-panel .info-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 4px 6px;
}

.airport-info-panel .info-value {
  color: #fff;
  font-weight: 500;
}

/* 鼠标经纬度显示样式 */
.mouse-coordinates {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 0;
  border-radius: 8px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  min-width: 280px;
  overflow: hidden;
}

.coordinates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(64, 158, 255, 0.8);
  padding: 6px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.coordinates-title {
  font-size: 11px;
  font-weight: 600;
  color: white;
}

.coordinates-controls {
  display: flex;
  gap: 4px;
}

.coordinate-close-btn {
  padding: 2px 4px !important;
  color: white !important;
  font-size: 12px !important;
  min-width: auto !important;
  height: 20px !important;
}

.coordinate-close-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

.coordinates-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 10px 12px;
}

.coordinate-line {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 1.4;
}

.coordinate-system {
  color: #ccc;
  font-weight: 500;
  font-size: 11px;
  min-width: 50px;
  margin-right: 4px;
}

.coordinate-value {
  color: #00ff88;
  font-weight: bold;
  letter-spacing: 0.5px;
  font-size: 12px;
  text-shadow: 0 0 2px rgba(0, 255, 136, 0.3);
  font-family: 'Courier New', monospace;
}

/* 坐标控制按钮 */
.coordinate-control-btn {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
}

.show-coordinates-btn {
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.show-coordinates-btn:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 地图工具栏样式 */
.map-toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.map-tool-btn {
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  /* 统一默认样式 */
  background: #f5f7fa !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
}

.map-tool-btn i,
.map-tool-btn .svg-icon {
  margin: 0 !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: inherit !important;
}

.map-tool-btn .svg-icon {
  width: 18px !important;
  height: 18px !important;
  fill: currentColor !important;
}

.map-tool-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  background: #ecf5ff !important;
  border-color: #b3d8ff !important;
  color: #409eff !important;
}

.map-tool-btn.active-tool {
  background: #409EFF !important;
  border-color: #409EFF !important;
  color: white !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3) !important;
}

.map-tool-btn.active-tool:hover {
  background: #337ecc !important;
  border-color: #337ecc !important;
  color: white !important;
}

.map-tool-btn.active-tool .svg-icon {
  fill: white !important;
}

/* 清除按钮特殊样式 */
.map-tool-btn-clear:hover {
  background: #fef0f0 !important;
  border-color: #fbc4c4 !important;
  color: #f56c6c !important;
}

.map-tool-btn-clear:active {
  background: #f56c6c !important;
  border-color: #f56c6c !important;
  color: white !important;
}

/* 测量点样式 (API 2.0 优化) */
.measure-point {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.measure-point:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.35);
}

.measure-point::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}

.measure-point-start {
  background: linear-gradient(135deg, #52c41a, #389e0d); /* 绿色渐变 - 起点 */
}

.measure-point-end {
  background: linear-gradient(135deg, #f5222d, #cf1322); /* 红色渐变 - 终点 */
}

.measure-point-mid {
  background: linear-gradient(135deg, #1890ff, #096dd9); /* 蓝色渐变 - 中间点 */
}

.measure-point-area {
  background: linear-gradient(135deg, #52c41a, #389e0d); /* 绿色渐变 - 面积顶点 */
}

/* 面积标签样式 (API 2.0 优化) */
.measure-area-label {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.95), rgba(82, 196, 26, 0.85));
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
  border: 2px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.measure-area-label:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* 距离标签样式 (API 2.0 新增) */
.measure-distance-label {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.95), rgba(24, 144, 255, 0.85));
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
  border: 2px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.measure-distance-label:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 14px rgba(0, 0, 0, 0.2);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .mouse-coordinates {
    bottom: 10px;
    left: 10px;
    font-size: 11px;
    min-width: 180px;
  }

  .coordinate-control-btn {
    bottom: 10px;
    left: 10px;
  }

  .show-coordinates-btn {
    width: 36px !important;
    height: 36px !important;
  }
}
</style>